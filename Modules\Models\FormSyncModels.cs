using System;
using System.Collections.Generic;
using System.Linq;

namespace ProManage.Modules.Models
{
    /// <summary>
    /// Result of form synchronization operation
    /// </summary>
    public class FormSyncResult
    {
        public bool HasMismatch { get; set; }
        public List<string> MissingForms { get; set; } = new List<string>();
        public List<string> ObsoleteForms { get; set; } = new List<string>();
        public List<string> ExistingForms { get; set; } = new List<string>();
        public bool SyncSuccess { get; set; }
        public List<string> Errors { get; set; } = new List<string>();
        public DateTime SyncTimestamp { get; set; }
        public TimeSpan SyncDuration { get; set; }
        public int TotalFormsProcessed { get; set; }
        public int UsersAffected { get; set; }
        public int RolesAffected { get; set; }
    }

    /// <summary>
    /// Information about a form file
    /// </summary>
    public class FormInfo
    {
        public string FormName { get; set; }
        public string FilePath { get; set; }
        public DateTime LastModified { get; set; }
        public bool IsValid { get; set; }
        public long FileSize { get; set; }
        public string NormalizedName { get; set; } // UpperInvariant version
    }

    /// <summary>
    /// Cache for form scan results with versioning support
    /// </summary>
    public class FormScanCache
    {
        public int Version { get; set; } = 1; // Cache schema version
        public DateTime LastScanTime { get; set; }
        public string FormListHash { get; set; }
        public List<string> CachedFormList { get; set; } = new List<string>();
        public string HashingAlgorithm { get; set; } = "SHA256"; // Track hashing method
        public bool IsValid => DateTime.Now.Subtract(LastScanTime).TotalMinutes < 30;
        public string CacheFilePath { get; set; }
        public long CacheFileSize { get; set; }
    }

    /// <summary>
    /// Progress reporting for sync operations
    /// </summary>
    public class SyncProgress
    {
        public int TotalOperations { get; set; }
        public int CompletedOperations { get; set; }
        public string CurrentOperation { get; set; }
        public string CurrentFormName { get; set; }
        public int PercentComplete => TotalOperations > 0 ? (CompletedOperations * 100) / TotalOperations : 0;
        public DateTime StartTime { get; set; }
        public TimeSpan ElapsedTime => DateTime.Now - StartTime;
        public TimeSpan EstimatedTimeRemaining 
        { 
            get 
            {
                if (CompletedOperations == 0) return TimeSpan.Zero;
                var avgTimePerOperation = ElapsedTime.TotalMilliseconds / CompletedOperations;
                var remainingOperations = TotalOperations - CompletedOperations;
                return TimeSpan.FromMilliseconds(avgTimePerOperation * remainingOperations);
            }
        }
    }

    /// <summary>
    /// Performance metrics for sync operations
    /// </summary>
    public class SyncPerformanceMetrics
    {
        public TimeSpan TotalSyncTime { get; set; }
        public int FormsAdded { get; set; }
        public int FormsRemoved { get; set; }
        public int UsersProcessed { get; set; }
        public int RolesProcessed { get; set; }
        public int DatabaseOperations { get; set; }
        public TimeSpan DatabaseTime { get; set; }
        public TimeSpan FileSystemTime { get; set; }
        public TimeSpan CacheTime { get; set; }
        public long MemoryUsed { get; set; }
        public bool CacheHit { get; set; }
    }

    /// <summary>
    /// Individual sync operation details
    /// </summary>
    public class SyncOperation
    {
        public SyncOperationType Type { get; set; }
        public string FormName { get; set; }
        public DateTime Timestamp { get; set; }
        public bool Success { get; set; }
        public string ErrorMessage { get; set; }
        public int RecordsAffected { get; set; }
    }

    /// <summary>
    /// Types of sync operations
    /// </summary>
    public enum SyncOperationType
    {
        AddForm,
        RemoveForm,
        ValidateForm,
        CacheUpdate,
        DatabaseSync
    }

    /// <summary>
    /// Detailed form mismatch information
    /// </summary>
    public class FormMismatchDetails
    {
        public List<string> FormsOnlyInFileSystem { get; set; } = new List<string>();
        public List<string> FormsOnlyInDatabase { get; set; } = new List<string>();
        public List<string> FormsInBoth { get; set; } = new List<string>();
        public int TotalMismatches => FormsOnlyInFileSystem.Count + FormsOnlyInDatabase.Count;
        public bool HasMismatches => TotalMismatches > 0;
    }

    /// <summary>
    /// Configuration for sync operations
    /// </summary>
    public class SyncConfiguration
    {
        public TimeSpan TransactionTimeout { get; set; } = TimeSpan.FromMinutes(5);
        public TimeSpan CacheExpiration { get; set; } = TimeSpan.FromMinutes(30);
        public string CacheDirectory { get; set; } = "%APPDATA%/ProManage";
        public string FallbackCacheDirectory { get; set; } = "%LOCALAPPDATA%/ProManage";
        public bool EnablePersistentCache { get; set; } = true;
        public bool EnableProgressReporting { get; set; } = true;
        public bool EnableCrossMachineLocking { get; set; } = true;
        public int MaxRetryAttempts { get; set; } = 3;
        public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);
    }

    /// <summary>
    /// Database configuration for sync operations
    /// </summary>
    public class DatabaseConfiguration
    {
        public string ConnectionString { get; set; }
        public bool UseAdvisoryLocks { get; set; } = true;
        public TimeSpan CommandTimeout { get; set; } = TimeSpan.FromMinutes(2);
        public bool EnableTransactionLogging { get; set; } = true;
        public string IsolationLevel { get; set; } = "Serializable";
    }

    /// <summary>
    /// Exception for form sync operations
    /// </summary>
    public class FormSyncException : Exception
    {
        public string FormName { get; set; }
        public SyncOperationType OperationType { get; set; }

        public FormSyncException(string message) : base(message) { }
        public FormSyncException(string message, Exception innerException) : base(message, innerException) { }
        public FormSyncException(string formName, SyncOperationType operationType, string message)
            : base(message)
        {
            FormName = formName;
            OperationType = operationType;
        }
    }

    /// <summary>
    /// Exception for cache operations
    /// </summary>
    public class CacheException : Exception
    {
        public string CacheFilePath { get; set; }
        public CacheOperationType OperationType { get; set; }

        public CacheException(string message) : base(message) { }
        public CacheException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Types of cache operations
    /// </summary>
    public enum CacheOperationType
    {
        Read,
        Write,
        Validate,
        Migrate,
        Clear
    }

    /// <summary>
    /// Health check result for operational monitoring
    /// </summary>
    public class HealthCheckResult
    {
        public string Status { get; set; }
        public DateTime? LastSyncTime { get; set; }
        public bool CacheValid { get; set; }
        public bool SyncInProgress { get; set; }
        public int CachedFormCount { get; set; }
        public DateTime Timestamp { get; set; }
        public string Version { get; set; }
        public string Message { get; set; }
    }

    /// <summary>
    /// Security validation result for Form Discovery Service
    /// </summary>
    public class SecurityValidationResult
    {
        public string ValidationId { get; set; }
        public DateTime ValidationTime { get; set; }
        public bool Passed { get; set; }
        public int PassedCount { get; set; }
        public int TotalCount { get; set; }
        public List<SecurityCheck> Checks { get; set; } = new List<SecurityCheck>();
        public string ErrorMessage { get; set; }
    }

    /// <summary>
    /// Individual security check for Form Discovery Service
    /// </summary>
    public class SecurityCheck
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Category { get; set; }
        public bool Passed { get; set; }
        public string Details { get; set; }
        public string Recommendation { get; set; }
    }

    /// <summary>
    /// System health status for monitoring
    /// </summary>
    public class SystemHealthStatus
    {
        public string Status { get; set; }
        public DateTime Timestamp { get; set; }
        public List<HealthComponent> Components { get; set; } = new List<HealthComponent>();
        public bool IsHealthy => Components.All(c => c.IsHealthy);
        public string OverallMessage { get; set; }
    }

    /// <summary>
    /// Individual health component
    /// </summary>
    public class HealthComponent
    {
        public string Name { get; set; }
        public string Status { get; set; }
        public bool IsHealthy { get; set; }
        public string Message { get; set; }
        public DateTime LastChecked { get; set; }
        public TimeSpan ResponseTime { get; set; }
        public string Category { get; set; }
        public string Details { get; set; }
        public string Recommendation { get; set; }
    }

    /// <summary>
    /// Database maintenance result
    /// </summary>
    public class DatabaseMaintenanceResult
    {
        public string MaintenanceId { get; set; }
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public List<MaintenanceOperation> Operations { get; set; } = new List<MaintenanceOperation>();
        public string ErrorMessage { get; set; }
        public int TotalOperations => Operations.Count;
        public int SuccessfulOperations => Operations.Count(o => o.Success);
    }

    /// <summary>
    /// Individual maintenance operation
    /// </summary>
    public class MaintenanceOperation
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public string ErrorMessage { get; set; }
        public int RecordsAffected { get; set; }
    }

    /// <summary>
    /// Maintenance schedule configuration
    /// </summary>
    public class MaintenanceSchedule
    {
        public string Name { get; set; }
        public TimeSpan Interval { get; set; }
        public DateTime LastRun { get; set; }
        public DateTime NextRun => LastRun.Add(Interval);
        public bool IsEnabled { get; set; }
        public string Description { get; set; }
    }
}
