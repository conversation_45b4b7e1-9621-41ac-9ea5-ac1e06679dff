using System;
using System.Drawing;
using System.Windows.Forms;
using System.Diagnostics;
using System.Threading.Tasks;
using ProManage.Modules.Connections;
using ProManage.Modules.Data.LoginForm;
using ProManage.Modules.Models.LoginForm;
using ProManage.Modules.Helpers;
using ProManage.Modules.Licensing;
using ProManage.Modules.Services;

namespace ProManage.Forms
{
    public partial class LoginForm : Form
    {
        // Flag to track if database is configured
        private bool _isDatabaseConfigured = false;

        // Flag to check if we're in design mode
        private readonly bool _isDesignMode = LicenseManager.UsageMode == LicenseUsageMode.Designtime;

        // Progress tracking fields (following ProgressIndicatorService pattern)
        private DateTime _progressShowTime = DateTime.MinValue;
        private bool _progressVisible = false;
        private readonly object _progressLock = new object();

        // Colors for UI elements
        private readonly Color _primaryButtonColor = Color.FromArgb(0, 122, 204);
        private readonly Color _hoverButtonColor = Color.FromArgb(0, 102, 184);
        private readonly Color _defaultButtonColor = Color.FromArgb(240, 240, 240);
        private readonly Color _successColor = Color.FromArgb(0, 130, 0);
        private readonly Color _warningColor = Color.FromArgb(200, 120, 0);
        private readonly Color _errorColor = Color.FromArgb(200, 0, 0);
        private readonly Color _infoColor = Color.FromArgb(0, 100, 160);

        // Enum for status message types
        private enum StatusMessageType
        {
            Success,
            Warning,
            Error,
            Info
        }

        public LoginForm()
        {
            InitializeComponent();

            // Set up the form
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.ShowIcon = false;
            this.Text = "ProManage 8.0 - Login";

            // Set up control styles
            SetupControlStyles();

            // Add event handlers
            this.Load += LoginForm_Load;
            btnLogin.Click += BtnLogin_Click;
            btnExit.Click += BtnExit_Click;
            btnDBConfig.Click += BtnDBConfig_Click;
            txtUsername.KeyDown += TxtUsername_KeyDown;
            txtPassword.KeyDown += TxtPassword_KeyDown;
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // Skip database checks in design mode
            if (_isDesignMode)
                return;

            // Check database connection
            CheckDatabaseConnection();

            // Update UI based on connection status
            // This is explicitly called to ensure the DBConfig button visibility is updated
            UpdateControlsState();
        }

        /// <summary>
        /// Sets up the visual styles for controls
        /// </summary>
        private void SetupControlStyles()
        {
            try
            {
                // Set up hover effects for buttons
                AddButtonHoverEffects(btnLogin, _primaryButtonColor, _hoverButtonColor, Color.White);
                AddButtonHoverEffects(btnExit, _defaultButtonColor, Color.FromArgb(200, 200, 200), Color.FromArgb(64, 64, 64));
                AddButtonHoverEffects(btnDBConfig, Color.FromArgb(245, 245, 245), Color.FromArgb(235, 235, 235), Color.FromArgb(64, 64, 64));

                // Set up focus effects for text boxes
                AddTextBoxFocusEffects(txtUsername);
                AddTextBoxFocusEffects(txtPassword);

                // Set up progress bar (following ProgressIndicatorService pattern)
                SetupLoginProgressBar();
            }
            catch (Exception ex)
            {
                // Log the error but continue
                System.Diagnostics.Debug.WriteLine($"Error setting up control styles: {ex.Message}");
            }
        }

        /// <summary>
        /// Adds hover effects to a button
        /// </summary>
        private void AddButtonHoverEffects(Button button, Color normalColor, Color hoverColor, Color textColor)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderColor = Color.FromArgb(200, 200, 200);
            button.FlatAppearance.BorderSize = 1;
            button.BackColor = normalColor;
            button.ForeColor = textColor;
            button.Cursor = Cursors.Hand;

            button.MouseEnter += (s, e) => button.BackColor = hoverColor;
            button.MouseLeave += (s, e) => button.BackColor = normalColor;
        }

        /// <summary>
        /// Adds focus effects to a text box
        /// </summary>
        private void AddTextBoxFocusEffects(TextBox textBox)
        {
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.BackColor = Color.FromArgb(250, 250, 250);

            textBox.Enter += (s, e) =>
            {
                textBox.BackColor = Color.White;
                textBox.BorderStyle = BorderStyle.FixedSingle;
            };

            textBox.Leave += (s, e) =>
            {
                textBox.BackColor = Color.FromArgb(250, 250, 250);
                textBox.BorderStyle = BorderStyle.FixedSingle;
            };
        }

        /// <summary>
        /// Sets up the login progress bar following ProgressIndicatorService patterns
        /// </summary>
        private void SetupLoginProgressBar()
        {
            try
            {
                // Configure the MarqueeProgressBarControl (following MainFrame pattern)
                LoginProgressBar.Properties.MarqueeAnimationSpeed = 50;
                LoginProgressBar.EditValue = 0;
                LoginProgressBar.Visible = false;

                Debug.WriteLine("LoginProgressBar configured successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up login progress bar: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows the login progress bar with status message (following ProgressIndicatorService pattern)
        /// </summary>
        /// <param name="message">Optional status message to display</param>
        private void ShowLoginProgress(string message = "")
        {
            lock (_progressLock)
            {
                try
                {
                    if (!_progressVisible)
                    {
                        // Record the time when the progress bar is shown (for minimum 250ms display)
                        _progressShowTime = DateTime.Now;

                        // Show the progress bar
                        if (LoginProgressBar.InvokeRequired)
                        {
                            LoginProgressBar.Invoke(new Action(() => LoginProgressBar.Visible = true));
                        }
                        else
                        {
                            LoginProgressBar.Visible = true;
                        }

                        _progressVisible = true;

                        // Update status message if provided
                        if (!string.IsNullOrEmpty(message))
                        {
                            ShowStatusMessage(message, StatusMessageType.Info);
                        }

                        // Force the UI to update immediately
                        // Using Refresh() instead of DoEvents() for better practice
                        this.Refresh();

                        Debug.WriteLine($"Login progress indicator shown" + (string.IsNullOrEmpty(message) ? "" : $" - {message}"));
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error showing login progress indicator: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Hides the login progress bar ensuring minimum 250ms display time (following ProgressIndicatorService pattern)
        /// </summary>
        private void HideLoginProgress()
        {
            lock (_progressLock)
            {
                try
                {
                    if (_progressVisible)
                    {
                        // Calculate how long the progress bar has been visible
                        var elapsedTime = DateTime.Now - _progressShowTime;
                        var minimumDisplayTime = TimeSpan.FromMilliseconds(250);

                        // If less than minimum time has passed, wait for the remainder
                        if (elapsedTime < minimumDisplayTime)
                        {
                            var remainingTime = minimumDisplayTime - elapsedTime;
                            System.Threading.Thread.Sleep((int)remainingTime.TotalMilliseconds);
                        }

                        // Hide the progress bar
                        if (LoginProgressBar.InvokeRequired)
                        {
                            LoginProgressBar.Invoke(new Action(() => LoginProgressBar.Visible = false));
                        }
                        else
                        {
                            LoginProgressBar.Visible = false;
                        }

                        _progressVisible = false;

                        Debug.WriteLine("Login progress indicator hidden");
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error hiding login progress indicator: {ex.Message}");
                }
            }
        }

        /// <summary>
        /// Checks the database connection status
        /// </summary>
        private void CheckDatabaseConnection()
        {
            // Show progress during database connection check
            ShowLoginProgress("Checking database connection...");

            try
            {
                // Check if database connection is configured
                _isDatabaseConfigured = DatabaseConnectionManager.Instance.IsConfigured;

                // If not configured, prompt the user to configure the database
                if (!_isDatabaseConfigured)
                {
                    DialogResult result = MessageBox.Show(
                        "Database connection is not configured. Would you like to configure it now?",
                        "Database Configuration Required",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // Show the database form
                        using (var dbForm = new ProManage.Forms.DatabaseForm())
                        {
                            dbForm.ShowDialog();
                        }

                        // Check again after configuration
                        _isDatabaseConfigured = DatabaseConnectionManager.Instance.IsConfigured;
                    }
                }

                // Update the connection status label
                if (_isDatabaseConfigured)
                {
                    // If not connected, try to establish the connection
                    if (!DatabaseConnectionManager.Instance.IsConnected)
                    {
                        if (DatabaseConnectionManager.Instance.OpenConnection())
                        {
                            // If connection is now open, start the monitoring
                            DatabaseConnectionManager.Instance.StartConnectionMonitoring();
                        }
                    }

                    // Check the current connection status again
                    if (DatabaseConnectionManager.Instance.IsConnected)
                    {
                        lblConnectionStatus.Text = "Database: Online";
                        lblConnectionStatus.ForeColor = _successColor;
                        ShowStatusMessage("Please login", StatusMessageType.Info);
                    }
                    else
                    {
                        lblConnectionStatus.Text = "Database: Offline";
                        lblConnectionStatus.ForeColor = _errorColor;
                        ShowStatusMessage($"Database connection failed: {DatabaseConnectionManager.Instance.LastError}", StatusMessageType.Error);
                    }
                }
                else
                {
                    lblConnectionStatus.Text = "Database: Not Configured";
                    lblConnectionStatus.ForeColor = _warningColor;
                    ShowStatusMessage("Please configure database settings", StatusMessageType.Warning);
                }
            }
            catch (Exception ex)
            {
                // Log the error
                System.Diagnostics.Debug.WriteLine($"Error checking database connection: {ex.Message}");

                // Update UI with error
                lblConnectionStatus.Text = "Database: Error";
                lblConnectionStatus.ForeColor = _errorColor;
                ShowStatusMessage($"Error: {ex.Message}", StatusMessageType.Error);
            }
            finally
            {
                // Always hide progress indicator
                HideLoginProgress();
            }

            // Update UI based on connection status
            UpdateControlsState();
        }

        /// <summary>
        /// Updates the status message with appropriate styling
        /// </summary>
        private void ShowStatusMessage(string message, StatusMessageType type)
        {
            lblStatus.Text = message;

            switch (type)
            {
                case StatusMessageType.Success:
                    lblStatus.ForeColor = _successColor;
                    break;
                case StatusMessageType.Warning:
                    lblStatus.ForeColor = _warningColor;
                    break;
                case StatusMessageType.Error:
                    lblStatus.ForeColor = _errorColor;
                    break;
                case StatusMessageType.Info:
                    lblStatus.ForeColor = _infoColor;
                    break;
            }
        }

        /// <summary>
        /// Updates the enabled state of controls based on database connection
        /// </summary>
        private void UpdateControlsState()
        {
            // Login button is enabled only if database is configured
            btnLogin.Enabled = _isDatabaseConfigured;

            // Username and password fields are enabled only if database is configured
            txtUsername.Enabled = _isDatabaseConfigured;
            txtPassword.Enabled = _isDatabaseConfigured;

            // Hide DB Config button if database is already configured
            btnDBConfig.Visible = !_isDatabaseConfigured;

            // If database is not configured, set focus to the DB Config button
            if (!_isDatabaseConfigured)
            {
                btnDBConfig.Focus();
            }
            else
            {
                txtUsername.Focus();
            }
        }

        /// <summary>
        /// Handles the Login button click
        /// </summary>
        private void BtnLogin_Click(object sender, EventArgs e)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                ShowStatusMessage("Please enter a username", StatusMessageType.Warning);
                txtUsername.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                ShowStatusMessage("Please enter a password", StatusMessageType.Warning);
                txtPassword.Focus();
                return;
            }

            // Show progress during login authentication
            ShowLoginProgress("Authenticating user...");

            try
            {
                // Validate user credentials against the database
                bool isAuthenticated = ValidateUserCredentials(txtUsername.Text, txtPassword.Text);

                if (!isAuthenticated)
                {
                    ShowStatusMessage("Invalid username or password", StatusMessageType.Error);
                    txtPassword.Clear();
                    txtPassword.Focus();
                    return;
                }

                // Set the current user
                var currentUser = new LoginFormUserModel(txtUsername.Text)
                {
                    // In a real application, you would set the full name from the database
                    FullName = txtUsername.Text
                };
                UserManager.Instance.SetCurrentUser(currentUser);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Authentication error: {ex.Message}");
                ShowStatusMessage($"Login error: {ex.Message}", StatusMessageType.Error);
                return;
            }
            finally
            {
                // Always hide progress indicator after authentication
                HideLoginProgress();
            }

            try
            {
                // Create the main MDI parent form (MainFrame) with a separate ApplicationContext
                // This approach ensures proper STA thread handling
                using (var mainFrame = new MainFrame())
                {
                    // Set the current user in the MainFrame
                    mainFrame.SetCurrentUser(UserManager.Instance.CurrentUser.FullName);

                    // Ensure the MainFrame is properly configured
                    mainFrame.WindowState = FormWindowState.Maximized;

                    // Hide the login form
                    this.Hide();

                    // Show the MainFrame as a dialog to ensure it stays in the foreground
                    // This also ensures we return to this code when the MainFrame is closed
                    mainFrame.ShowDialog();

                    // When we get here, the MainFrame has been closed
                    // Show the login form again
                    this.Show();
                    txtUsername.Clear();
                    txtPassword.Clear();

                    // Update the login message based on connection status
                    if (_isDatabaseConfigured)
                    {
                        ShowStatusMessage("Please login", StatusMessageType.Info);
                    }
                    else
                    {
                        ShowStatusMessage("Please configure database settings", StatusMessageType.Warning);
                    }
                }

                Debug.WriteLine("MainFrame session completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error with MainFrame: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Debug.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                }

                MessageBox.Show($"Error showing MainFrame: {ex.Message}\n\nInner Exception: {ex.InnerException?.Message}",
                    "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);

                // Show the login form again if there was an error
                this.Show();
                txtUsername.Clear();
                txtPassword.Clear();
                ShowStatusMessage($"Error: {ex.Message}", StatusMessageType.Error);
            }
        }

        /// <summary>
        /// Handles the Exit button click
        /// </summary>
        private void BtnExit_Click(object sender, EventArgs e)
        {
            System.Windows.Forms.Application.Exit();
        }

        /// <summary>
        /// Handles the DB Config button click
        /// </summary>
        private void BtnDBConfig_Click(object sender, EventArgs e)
        {
            // Open the database configuration form
            var dbForm = new DatabaseForm();
            DialogResult result = dbForm.ShowDialog();

            // Check if configuration was successful (OK result)
            if (result == DialogResult.OK)
            {
                Debug.WriteLine("Database configuration was successful");

                // After the form closes with OK result, check the database connection again
                CheckDatabaseConnection();

                // Update UI controls based on the current connection status
                UpdateControlsState();

                // Show success message
                ShowStatusMessage("Database configured successfully", StatusMessageType.Success);
            }
            else
            {
                // Even if canceled, we should check the connection status
                CheckDatabaseConnection();

                // Update UI controls based on the current connection status
                UpdateControlsState();

                // Show appropriate message based on whether the database is configured
                if (_isDatabaseConfigured)
                {
                    ShowStatusMessage("Database is already configured", StatusMessageType.Info);
                }
                else
                {
                    ShowStatusMessage("Database configuration required", StatusMessageType.Warning);
                }
            }
        }

        /// <summary>
        /// Handles key down event for username text box
        /// </summary>
        private void TxtUsername_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.SuppressKeyPress = true; // Suppress the beep
                txtPassword.Focus();
            }
        }

        /// <summary>
        /// Handles key down event for password text box
        /// </summary>
        private void TxtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                e.SuppressKeyPress = true; // Suppress the beep
                BtnLogin_Click(sender, e);
            }
        }

        /// <summary>
        /// Validates user credentials against the database
        /// </summary>
        /// <param name="username">The username to validate</param>
        /// <param name="password">The password to validate</param>
        /// <returns>True if credentials are valid, false otherwise</returns>
        private bool ValidateUserCredentials(string username, string password)
        {
            try
            {
                // For demonstration purposes, we'll accept any non-empty credentials
                // In a real application, you would validate against the database

                // Simulate authentication delay
                System.Threading.Thread.Sleep(1000);

                // TODO: Implement actual database validation
                // Example of how this would be implemented:
                /*
                using (var conn = DatabaseConnectionManager.Instance.GetConnection())
                {
                    using (var cmd = new Npgsql.NpgsqlCommand())
                    {
                        cmd.Connection = conn;
                        cmd.CommandText = "SELECT user_id FROM users WHERE username = @username AND password_hash = @password_hash";
                        cmd.Parameters.AddWithValue("@username", username);
                        cmd.Parameters.AddWithValue("@password_hash", HashPassword(password));

                        var result = cmd.ExecuteScalar();
                        return result != null;
                    }
                }
                */

                // For now, just check if credentials are not empty
                return !string.IsNullOrWhiteSpace(username) && !string.IsNullOrWhiteSpace(password);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating credentials: {ex.Message}");
                throw new Exception("Error validating credentials", ex);
            }
        }
    }
}
